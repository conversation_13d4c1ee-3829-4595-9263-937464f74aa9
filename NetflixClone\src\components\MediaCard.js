import React, { memo, useMemo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Animated,
  ImageBackground
} from 'react-native';
import { slothStyles } from '../styles/sloth';
import tmdbApi from '../services/tmdbApi';

// ADD a gridMode prop. Default to false.
const MediaCard = memo(({ item, onPress, gridMode = false }) => {
  const scaleValue = useMemo(() => new Animated.Value(1), []);

  const handlePressIn = () => {
    Animated.spring(scaleValue, { toValue: 0.96, useNativeDriver: true }).start();
  };
  const handlePressOut = () => {
    Animated.spring(scaleValue, { toValue: 1, useNativeDriver: true }).start();
  };

  const imageUrl = tmdbApi.getPosterUrl(item.poster_path);

  // NEW: Choose styles based on gridMode
  const containerStyle = gridMode ? slothStyles.gridCardContainer : slothStyles.mediaCardContainer;
  const imageStyle = gridMode ? slothStyles.gridCardImage : slothStyles.mediaCardImage;

  return (
    // The animated container now uses the dynamic style
    <Animated.View style={[{ transform: [{ scale: scaleValue }] }, containerStyle]}>
      <TouchableOpacity
        onPress={() => onPress(item)}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={0.9}
      >
        <ImageBackground
          source={{ uri: imageUrl }}
          style={imageStyle} // Use the dynamic image style
          imageStyle={{ borderRadius: 12 }}
        >
          {/* S Logo Overlay: Conditionally render if NOT in grid mode */}
         
        </ImageBackground>
      </TouchableOpacity>
    </Animated.View>
  );
});

export default MediaCard;