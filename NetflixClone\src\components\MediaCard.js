import React, { memo, useMemo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Animated,
  ImageBackground
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { slothStyles, SLOTH_COLORS } from '../styles/sloth';
import tmdbApi from '../services/tmdbApi';

// Enhanced MediaCard with continue watching support
const MediaCard = memo(({
  item,
  onPress,
  gridMode = false,
  watchProgress = null, // { progressPercent, episode, season }
  showRemoveButton = false,
  onRemove = null
}) => {
  const scaleValue = useMemo(() => new Animated.Value(1), []);

  const handlePressIn = () => {
    Animated.spring(scaleValue, { toValue: 0.96, useNativeDriver: true }).start();
  };
  const handlePressOut = () => {
    Animated.spring(scaleValue, { toValue: 1, useNativeDriver: true }).start();
  };

  const imageUrl = tmdbApi.getPosterUrl(item.poster_path);

  // Choose styles based on gridMode
  const containerStyle = gridMode ? slothStyles.gridCardContainer : slothStyles.mediaCardContainer;
  const imageStyle = gridMode ? slothStyles.gridCardImage : slothStyles.mediaCardImage;

  return (
    <Animated.View style={[{ transform: [{ scale: scaleValue }] }, containerStyle]}>
      <TouchableOpacity
        onPress={() => onPress(item)}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        activeOpacity={0.9}
      >
        <ImageBackground
          source={{ uri: imageUrl }}
          style={imageStyle}
          imageStyle={{ borderRadius: 12 }}
        >
          {/* Remove button for continue watching */}
          {showRemoveButton && onRemove && (
            <TouchableOpacity
              style={slothStyles.removeButton}
              onPress={(e) => {
                e.stopPropagation();
                onRemove(item);
              }}
            >
              <Ionicons name="close" size={16} color={SLOTH_COLORS.white} />
            </TouchableOpacity>
          )}

          {/* Episode info overlay for TV shows */}
          {watchProgress?.episode && watchProgress?.season && (
            <View style={slothStyles.episodeInfoOverlay}>
              <Text style={slothStyles.episodeInfoText}>
                S{watchProgress.season.season_number}:E{watchProgress.episode.episode_number}
              </Text>
            </View>
          )}
        </ImageBackground>
      </TouchableOpacity>

      {/* Progress bar */}
      {watchProgress && watchProgress.progressPercent > 0 && (
        <View style={slothStyles.progressBarContainer}>
          <View style={slothStyles.progressBarBackground}>
            <View
              style={[
                slothStyles.progressBarFill,
                { width: `${Math.min(watchProgress.progressPercent * 100, 100)}%` }
              ]}
            />
          </View>
        </View>
      )}
    </Animated.View>
  );
});

export default MediaCard;