import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  ScrollView,
  StatusBar,
  Text,
  TouchableOpacity,
  ImageBackground,
  Image,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { slothStyles, SLOTH_COLORS } from '../styles/sloth';
import tmdbApi from '../services/tmdbApi';
import WatchHistoryService from '../services/WatchHistoryService';
import MediaRow from '../components/MediaRow';
import CastRow from '../components/CastRow';
import VideoRow from '../components/VideoRow';

// Enhanced MediaDetailHeader with resume functionality
const MediaDetailHeader = ({ item, mediaType, navigation }) => {
  const [watchProgress, setWatchProgress] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadWatchProgress = async () => {
      try {
        setLoading(true);
        const progress = await WatchHistoryService.getProgress(item, mediaType);
        setWatchProgress(progress);
      } catch (error) {
        console.error('Error loading watch progress:', error);
      } finally {
        setLoading(false);
      }
    };

    loadWatchProgress();
  }, [item.id, mediaType]);

  const handlePlayPress = () => {
    if (mediaType === 'tv') {
        navigation.navigate('TVEpisodes', { item: item });
    } else {
        navigation.navigate('Player', {
            item: item,
            mediaType: 'movie'
        });
    }
  };

  const handleResumePress = () => {
    if (mediaType === 'tv') {
        navigation.navigate('TVEpisodes', { item: item });
    } else {
        navigation.navigate('Player', {
            item: item,
            mediaType: 'movie'
        });
    }
  };

  const formatProgressText = () => {
    if (!watchProgress) return '';
    const percent = Math.round(watchProgress.progressPercent * 100);
    return `${percent}% watched`;
  };

  const shouldShowResume = watchProgress && watchProgress.progressPercent > 0.05 && watchProgress.progressPercent < 0.9;
  const shouldShowWatchAgain = watchProgress && watchProgress.progressPercent >= 0.9;
  const playButtonLabel = shouldShowWatchAgain ? 'Watch Again' : (mediaType === 'tv' ? 'View Seasons' : 'Play');

  return (
    <View>
      {/* HEADER: Contains backdrop */}
      <View>
        <ImageBackground
          source={{ uri: tmdbApi.getBackdropUrl(item.backdrop_path) }}
          style={slothStyles.detailBackdrop}
        >
          <LinearGradient
            colors={['rgba(15,16,20,0.1)', SLOTH_COLORS.background]}
            style={{ flex: 1 }}
          />
        </ImageBackground>
      </View>

      {/* MAIN CONTENT Area (everything except "More Like This") */}
      <View style={slothStyles.detailContent}>
        {/* Poster + Title Block */}
        <View style={slothStyles.detailPosterInfoBlock}>
          <Image
            source={{ uri: tmdbApi.getPosterUrl(item.poster_path) }}
            style={slothStyles.detailPosterImage}
          />
          <View style={slothStyles.detailTitleBlock}>
            <Text style={slothStyles.detailTitle}>{item.title || item.name}</Text>
            <View style={slothStyles.detailMetaRow}>
              {item.vote_average > 0 && (
                <>
                  <Ionicons name="star" size={14} style={slothStyles.detailStarIcon}/>
                  <Text style={slothStyles.detailMetaText}>{item.vote_average.toFixed(1)}</Text>
                </>
              )}
              <Text style={slothStyles.detailMetaText}>
                {new Date(item.release_date || item.first_air_date).getFullYear()}
              </Text>
            </View>
          </View>
        </View>

        {/* Progress Info */}
        {watchProgress && watchProgress.progressPercent > 0.05 && (
          <View style={slothStyles.detailProgressInfo}>
            <View style={slothStyles.detailProgressBar}>
              <View
                style={[
                  slothStyles.detailProgressFill,
                  { width: `${Math.min(watchProgress.progressPercent * 100, 100)}%` }
                ]}
              />
            </View>
            <Text style={slothStyles.detailProgressText}>
              {formatProgressText()}
            </Text>
          </View>
        )}

        {/* Action Buttons */}
        <View style={slothStyles.detailMainActions}>
          {shouldShowResume && (
            <TouchableOpacity style={slothStyles.detailResumeButton} onPress={handleResumePress}>
              <Ionicons name="play" size={20} color={SLOTH_COLORS.background} />
              <Text style={slothStyles.detailResumeButtonText}>
                {mediaType === 'tv' && watchProgress.episode
                  ? `Resume S${watchProgress.season?.season_number}:E${watchProgress.episode?.episode_number}`
                  : 'Resume'
                }
              </Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={[
              slothStyles.detailPlayButton,
              shouldShowResume && { flex: 0.8 }
            ]}
            onPress={handlePlayPress}
          >
            <Ionicons name="play" size={20} color={SLOTH_COLORS.white} />
            <Text style={slothStyles.detailPlayButtonText}>{playButtonLabel}</Text>
          </TouchableOpacity>

          <TouchableOpacity style={slothStyles.detailIconBtn}>
            <Ionicons name="add" size={26} color={SLOTH_COLORS.white} />
          </TouchableOpacity>
        </View>

        {/* Synopsis */}
        <View>
          <Text style={slothStyles.detailSectionTitle}>Synopsis</Text>
          <Text style={slothStyles.detailOverview}>{item.overview}</Text>
        </View>
      </View>
    </View>
  );
};

const MediaDetailScreen = ({ route, navigation }) => {
  const { item: initialItem, mediaType } = route.params;
  const [item, setItem] = useState(initialItem);
  const [loading, setLoading] = useState(true);
  const [relatedContent, setRelatedContent] = useState([]);
  const [castData, setCastData] = useState([]);
  const [videoData, setVideoData] = useState([]);
  const insets = useSafeAreaInsets();
  const scrollViewRef = useRef(null);

  useEffect(() => {
    scrollViewRef.current?.scrollTo({ y: 0, animated: false });
    loadAllData();

    // Completely disable the back gesture to test if it's causing scrolling issues
    navigation.setOptions({
      gestureEnabled: false,
    });
  }, [initialItem.id, navigation]);

  const loadAllData = async () => {
    try {
      setLoading(true);
      const detailData = mediaType === 'tv'
        ? await tmdbApi.getTVDetails(initialItem.id)
        : await tmdbApi.getMovieDetails(initialItem.id);

      setItem(detailData);

      // Process related content
      const recommendations = detailData.recommendations?.results || [];
      const similar = detailData.similar?.results || [];
      const combined = [...recommendations, ...similar];
      const uniqueContent = Array.from(new Map(combined.map(c => [c.id, c])).values())
        .filter(c => c.poster_path);
      setRelatedContent(uniqueContent);

      // Process cast data
      const cast = detailData.credits?.cast || [];
      const filteredCast = cast
        .filter(member => member.profile_path && member.name && member.character)
        .slice(0, 20); // Limit to first 20 cast members
      setCastData(filteredCast);

      // Process video data
      const videos = detailData.videos?.results || [];
      const filteredVideos = videos
        .filter(video =>
          video.site === 'YouTube' &&
          (video.type === 'Trailer' || video.type === 'Teaser' || video.type === 'Clip') &&
          video.key
        )
        .slice(0, 10); // Limit to first 10 videos
      setVideoData(filteredVideos);

    } catch (error) {
      console.error("Error loading full detail screen:", error);
      Alert.alert("Error", "Could not load all details. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  const handleItemPress = (selectedItem) => {
    const nextMediaType = selectedItem.media_type || (selectedItem.first_air_date ? 'tv' : 'movie');
    navigation.push('MediaDetail', { item: selectedItem, mediaType: nextMediaType });
  };
  
  if (loading) {
    return <View style={slothStyles.loadingContainer}><ActivityIndicator size="large" color={SLOTH_COLORS.primary} /></View>;
  }

  return (
    <View style={slothStyles.container}>
      <StatusBar barStyle="light-content" />

      {/* Main scrollable content */}
      <ScrollView
        ref={scrollViewRef}
        style={{ flex: 1 }}
        showsVerticalScrollIndicator={false}
        nestedScrollEnabled={true}
        scrollEventThrottle={16}
        contentContainerStyle={{ paddingBottom: 40 }}
      >
        <MediaDetailHeader
          item={item}
          mediaType={mediaType}
          navigation={navigation}
        />

        {/* Cast section */}
        {castData.length > 0 && (
          <CastRow
            title="Cast"
            data={castData}
            loading={loading}
          />
        )}

        {/* Trailers & Videos section */}
        {videoData.length > 0 && (
          <VideoRow
            title="Trailers & Videos"
            data={videoData}
            loading={loading}
          />
        )}

        {/* More Like This section */}
        {relatedContent.length > 0 && (
          <View>
            <MediaRow
              title="More Like This"
              data={relatedContent}
              onItemPress={handleItemPress}
            />
          </View>
        )}
      </ScrollView>

      {/* Back button overlay */}
      <View style={[slothStyles.detailHeader, { top: insets.top }]}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={slothStyles.iconContainer}
        >
          <Ionicons name="arrow-back" size={24} color={SLOTH_COLORS.white} />
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default MediaDetailScreen;