import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { netflixStyles } from '../styles/netflix';
import { NETFLIX_COLORS } from '../utils/constants';
import MediaCard from './MediaCard';
import storageService from '../services/storage';

const ContinueWatching = ({ onItemPress, onRefresh }) => {
  const [continueWatchingItems, setContinueWatchingItems] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadContinueWatching();
  }, []);

  const loadContinueWatching = async () => {
    try {
      setLoading(true);
      const items = await storageService.getContinueWatching();
      setContinueWatchingItems(items);
    } catch (error) {
      console.error('Error loading continue watching:', error);
    } finally {
      setLoading(false);
    }
  };

  const getProgress = (item) => {
    return item.progress || 0;
  };

  const handleRemoveItem = async (item) => {
    Alert.alert(
      'Remove from Continue Watching',
      `Remove "${item.title || item.name}" from your continue watching list?`,
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              await storageService.removeFromWatchHistory(item.id, item.type);
              await loadContinueWatching();
              onRefresh && onRefresh();
            } catch (error) {
              console.error('Error removing item:', error);
            }
          },
        },
      ]
    );
  };

  const handleItemPress = (item) => {
    onItemPress(item);
  };

  if (loading) {
    return null; // Don't show loading state for continue watching
  }

  if (continueWatchingItems.length === 0) {
    return null; // Don't show section if no items
  }

  return (
    <View style={netflixStyles.mediaRow}>
      <View style={{ 
        flexDirection: 'row', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginHorizontal: 20,
        marginBottom: 10,
      }}>
        <Text style={netflixStyles.mediaRowTitle}>Continue Watching</Text>
        <TouchableOpacity onPress={loadContinueWatching}>
          <Ionicons 
            name="refresh" 
            size={20} 
            color={NETFLIX_COLORS.lightGray} 
          />
        </TouchableOpacity>
      </View>
      
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={netflixStyles.mediaRowScroll}
        decelerationRate="fast"
        snapToInterval={130}
        snapToAlignment="start"
      >
        {continueWatchingItems.map((item, index) => (
          <View key={`${item.id}-${index}`} style={{ position: 'relative' }}>
            <MediaCard
              item={item}
              onPress={handleItemPress}
              size="normal"
              showProgress={true}
              progress={getProgress(item)}
            />
            
            <TouchableOpacity
              style={{
                position: 'absolute',
                top: 5,
                right: 15,
                backgroundColor: 'rgba(0, 0, 0, 0.7)',
                borderRadius: 12,
                width: 24,
                height: 24,
                justifyContent: 'center',
                alignItems: 'center',
              }}
              onPress={() => handleRemoveItem(item)}
            >
              <Ionicons 
                name="close" 
                size={16} 
                color={NETFLIX_COLORS.white} 
              />
            </TouchableOpacity>
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

export default ContinueWatching;
