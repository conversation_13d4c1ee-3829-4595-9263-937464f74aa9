import React, { useState, useEffect, useRef, useCallback } from 'react';
import { View, ActivityIndicator, Alert, StatusBar, Text, TouchableOpacity, StyleSheet, Animated, Platform, AppState, Modal, FlatList } from 'react-native';
import { WebView } from 'react-native-webview';
import Slider from '@react-native-community/slider';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useIsFocused } from '@react-navigation/native';
import * as NavigationBar from 'expo-navigation-bar';

import { slothStyles as baseSlothStyles, SLOTH_COLORS } from '../styles/sloth';
import soraApi from '../services/soraApi';
import tmdbApi from '../services/tmdbApi';
import WatchHistoryService from '../services/WatchHistoryService';
import { PROXY_BASE_URL } from '../utils/constants';

// FIX: Updated the time formatting function to handle hours.
const formatTime = (timeInSeconds) => {
    if (isNaN(timeInSeconds) || timeInSeconds < 0) {
        return '00:00';
    }

    // Calculate hours, minutes, and seconds
    const hours = Math.floor(timeInSeconds / 3600);
    const minutes = Math.floor((timeInSeconds % 3600) / 60);
    const seconds = Math.floor(timeInSeconds % 60);

    // Pad minutes and seconds with a leading zero if they are less than 10
    const paddedMinutes = minutes.toString().padStart(2, '0');
    const paddedSeconds = seconds.toString().padStart(2, '0');

    // Only include hours in the string if the duration is an hour or more
    if (hours > 0) {
        return `${hours}:${paddedMinutes}:${paddedSeconds}`;
    } else {
        return `${paddedMinutes}:${paddedSeconds}`;
    }
};

const generatePlayerHTML = (proxiedVideoUrl, subtitles = []) => `
  <!DOCTYPE html>
  <html>
  <head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <style>
      body, html { margin: 0; padding: 0; width: 100%; height: 100%; background-color: #000; overflow: hidden; }
      video { width: 100%; height: 100%; }
      video::cue {
        background-color: rgba(0, 0, 0, 0.8);
        color: white;
        font-size: 18px;
        font-family: Arial, sans-serif;
        text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.8);
      }
    </style>
  </head>
  <body>
    <video id="player" autoplay playsinline crossorigin="anonymous">
      <source src="${proxiedVideoUrl}" type="video/mp4">
      ${subtitles.map((sub, index) =>
        `<track kind="subtitles" src="${sub.url}" srclang="${sub.lang || 'en'}" label="${sub.label || sub.lang || `Subtitle ${index + 1}`}" ${index === 0 ? 'default' : ''}>`
      ).join('')}
    </video>
    <script>
      const video = document.getElementById('player');
      const postMessage = (data) => window.ReactNativeWebView.postMessage(JSON.stringify(data));

      const log = (message, data) => postMessage({ type: 'DEBUG', payload: { message, data: data || null } });

      video.addEventListener('loadedmetadata', () => {
          postMessage({ type: 'LOADED', payload: { duration: video.duration } });
          log('Video metadata loaded.', { duration: video.duration, tracks: video.textTracks.length });
      });
      video.addEventListener('play', () => postMessage({ type: 'PLAYING' }));
      video.addEventListener('pause', () => postMessage({ type: 'PAUSED' }));
      video.addEventListener('waiting', () => postMessage({ type: 'BUFFERING' }));
      video.addEventListener('playing', () => postMessage({ type: 'PLAYING' }));
      video.addEventListener('error', () => postMessage({ type: 'ERROR', payload: { error: video.error ? video.error.message : 'Unknown Video Error' } }));
      video.addEventListener('timeupdate', () => postMessage({ type: 'STATUS_UPDATE', payload: { currentTime: video.currentTime } }));
      video.addEventListener('ended', () => postMessage({ type: 'VIDEO_ENDED' }));

      window.handlePlayerCommand = (command, data) => {
        switch(command) {
          case 'PLAY': video.play(); break;
          case 'PAUSE': video.pause(); break;
          case 'SEEK': video.currentTime = data.time; break;
          case 'SEEK_BY': video.currentTime += data.amount; break;
          case 'CHANGE_SOURCE':
            const currentTime = video.currentTime;
            video.src = data.url;
            video.load();
            video.addEventListener('loadedmetadata', () => {
              video.currentTime = currentTime;
              video.play();
            }, { once: true });
            break;
          case 'TOGGLE_SUBTITLES':
            const tracks = video.textTracks;
            for (let i = 0; i < tracks.length; i++) {
              tracks[i].mode = i === data.index ? 'showing' : 'hidden';
            }
            break;
        }
      };
    </script>
  </body>
  </html>`;

const PlayerScreen = ({ route, navigation }) => {
    const { item, mediaType } = route.params;
    const insets = useSafeAreaInsets();
    const webviewRef = useRef(null);
    const isFocused = useIsFocused();

    const [playerHtml, setPlayerHtml] = useState(null);
    const [isPlayerReady, setIsPlayerReady] = useState(false);
    const [isVideoLoading, setIsVideoLoading] = useState(true);
    const [error, setError] = useState(null);
    const [controlsVisible, setControlsVisible] = useState(true);
    const [isPlaying, setIsPlaying] = useState(false);
    const [duration, setDuration] = useState(0);
    const [currentTime, setCurrentTime] = useState(0);

    const [availableStreams, setAvailableStreams] = useState([]);
    const [currentStreamIndex, setCurrentStreamIndex] = useState(0);
    const [availableSubtitles, setAvailableSubtitles] = useState([]);
    const [currentSubtitleIndex, setCurrentSubtitleIndex] = useState(-1);
    const [showQualityModal, setShowQualityModal] = useState(false);
    const [showSubtitleModal, setShowSubtitleModal] = useState(false);

    // TV Show specific states
    const [showEpisodeListModal, setShowEpisodeListModal] = useState(false);
    const [showSeasonModal, setShowSeasonModal] = useState(false);
    const [showAutoNextModal, setShowAutoNextModal] = useState(false);
    const [autoNextCountdown, setAutoNextCountdown] = useState(15);
    const [allSeasons, setAllSeasons] = useState([]);
    const [selectedSeasonForList, setSelectedSeasonForList] = useState(null);
    const [currentSeasonEpisodes, setCurrentSeasonEpisodes] = useState([]);
    const [nextEpisode, setNextEpisode] = useState(null);

    // Watch history tracking
    const [hasLoadedSavedProgress, setHasLoadedSavedProgress] = useState(false);
    const progressSaveInterval = useRef(null);
    const lastSavedTime = useRef(0);

    const controlsOpacity = useRef(new Animated.Value(1)).current;
    const controlsTimeout = useRef(null);
    const API_BASE_URL = 'https://sorastream-five.vercel.app';

    // Immersive Mode Logic
    const applyImmersiveMode = async () => {
        if (Platform.OS === 'android') {
            StatusBar.setHidden(true, 'fade');
            try {
                await NavigationBar.setVisibilityAsync('hidden');
                await NavigationBar.setBehaviorAsync('overlay-swipe');
            } catch (error) {
                console.error('Error applying immersive mode:', error);
            }
        }
    };

    const exitImmersiveMode = async () => {
        if (Platform.OS === 'android') {
            StatusBar.setHidden(false, 'fade');
            try {
                await NavigationBar.setVisibilityAsync('visible');
            } catch (error) {
                console.error('Error exiting immersive mode:', error);
            }
        }
    };

    useEffect(() => {
        if (isFocused) {
            applyImmersiveMode();
        } else {
            exitImmersiveMode();
        }
        return () => exitImmersiveMode();
    }, [isFocused]);

    useEffect(() => {
        if (isFocused && !controlsVisible && isPlaying) {
            const timer = setTimeout(() => applyImmersiveMode(), 300);
            return () => clearTimeout(timer);
        }
    }, [controlsVisible, isPlaying, isFocused]);

    useEffect(() => {
        const handleAppStateChange = (nextAppState) => {
            if (nextAppState === 'active' && isFocused && !controlsVisible && isPlaying) {
                setTimeout(() => applyImmersiveMode(), 100);
            }
        };
        const subscription = AppState.addEventListener('change', handleAppStateChange);
        return () => subscription?.remove();
    }, [isFocused, controlsVisible, isPlaying]);

    useEffect(() => {
        let interval;
        if (isFocused && !controlsVisible && isPlaying) {
            interval = setInterval(() => {
                applyImmersiveMode();
            }, 5000);
        }
        return () => {
            if (interval) clearInterval(interval);
        };
    }, [isFocused, controlsVisible, isPlaying]);
    // End Immersive Mode Logic

    useEffect(() => {
        const preparePlayer = async () => {
            try {
                const streamData = mediaType === 'movie'
                    ? await soraApi.getMovieStreams(item.id)
                    : await soraApi.getTVStreams(item.id, route.params.season.season_number, route.params.episode.episode_number);

                const processedData = soraApi.processStreamResponse(streamData);

                if (processedData && processedData.streams && processedData.streams.length > 0) {
                    setAvailableStreams(processedData.streams);
                    setAvailableSubtitles(processedData.subtitles || []);

                    console.log('Available streams:', processedData.streams.length);
                    console.log('Available subtitles:', (processedData.subtitles || []).length);

                    const bestStream = soraApi.getBestQualityStream(processedData);
                    const proxiedStreamUrl = `${PROXY_BASE_URL}${encodeURIComponent(bestStream.url)}`;

                    const fullUrlSubtitles = (processedData.subtitles || []).map(sub => ({
                        ...sub,
                        url: sub.url ? `${API_BASE_URL}${sub.url}` : null,
                        label: sub.lang
                    })).filter(sub => sub.url);

                    console.log('Processed Subtitle URLs:', fullUrlSubtitles.map(s => s.url));

                    setPlayerHtml(generatePlayerHTML(proxiedStreamUrl, fullUrlSubtitles));
                } else {
                    throw new Error('No playable stream was found.');
                }
            } catch (e) {
                setError(e.message);
                Alert.alert("Playback Error", `Could not prepare the video: ${e.message}`, [{ text: "OK", onPress: () => navigation.goBack() }]);
            }
        };
        preparePlayer();
    }, [item.id, mediaType, route.params.season, route.params.episode]);

    // Load TV show data for episode navigation
    useEffect(() => {
        const loadTVShowData = async () => {
            if (mediaType === 'tv' && route.params.season && route.params.episode) {
                try {
                    // Load TV show details to get all seasons
                    const tvDetails = await tmdbApi.getTVDetails(item.id);
                    const availableSeasons = (tvDetails.seasons || []).filter(s => s.season_number > 0 && s.episode_count > 0);
                    setAllSeasons(availableSeasons);

                    // Set current season for episode list
                    const currentSeason = availableSeasons.find(s => s.season_number === route.params.season.season_number) || route.params.season;
                    setSelectedSeasonForList(currentSeason);

                    // Load current season episodes
                    const seasonDetails = await tmdbApi.getTVSeasonDetails(item.id, route.params.season.season_number);
                    const episodes = seasonDetails.episodes || [];
                    setCurrentSeasonEpisodes(episodes);

                    // Find next episode
                    const currentEpisodeIndex = episodes.findIndex(ep => ep.episode_number === route.params.episode.episode_number);
                    if (currentEpisodeIndex >= 0 && currentEpisodeIndex < episodes.length - 1) {
                        setNextEpisode(episodes[currentEpisodeIndex + 1]);
                    } else {
                        // Check if there's a next season
                        const currentSeasonIndex = availableSeasons.findIndex(s => s.season_number === route.params.season.season_number);
                        if (currentSeasonIndex >= 0 && currentSeasonIndex < availableSeasons.length - 1) {
                            const nextSeason = availableSeasons[currentSeasonIndex + 1];
                            const nextSeasonDetails = await tmdbApi.getTVSeasonDetails(item.id, nextSeason.season_number);
                            if (nextSeasonDetails.episodes && nextSeasonDetails.episodes.length > 0) {
                                setNextEpisode({
                                    ...nextSeasonDetails.episodes[0],
                                    season: nextSeason
                                });
                            }
                        }
                    }
                } catch (error) {
                    console.error('Error loading TV show data:', error);
                }
            }
        };

        loadTVShowData();
    }, [item.id, mediaType, route.params.season, route.params.episode]);

    const showControls = useCallback((autoHide = true) => {
        clearTimeout(controlsTimeout.current);
        Animated.timing(controlsOpacity, { toValue: 1, duration: 200, useNativeDriver: true }).start();
        setControlsVisible(true);
        if (autoHide && isPlaying) {
            controlsTimeout.current = setTimeout(hideControls, 4000);
        }
    }, [isPlaying, controlsOpacity]);

    const hideControls = useCallback(() => {
        clearTimeout(controlsTimeout.current);
        Animated.timing(controlsOpacity, { toValue: 0, duration: 200, useNativeDriver: true }).start(() => {
            setControlsVisible(false);
            if (isFocused && isPlaying) {
                setTimeout(() => applyImmersiveMode(), 100);
            }
        });
    }, [isFocused, isPlaying, controlsOpacity]);

    const handleScreenTap = useCallback(() => controlsVisible ? hideControls() : showControls(), [controlsVisible, hideControls, showControls]);

    const sendCommand = (command, data = null) =>
        webviewRef.current?.injectJavaScript(`window.handlePlayerCommand('${command}', ${JSON.stringify(data)}); true;`);

    const switchQuality = useCallback((streamIndex) => {
        if (streamIndex >= 0 && streamIndex < availableStreams.length) {
            const selectedStream = availableStreams[streamIndex];
            const proxiedStreamUrl = `${PROXY_BASE_URL}${encodeURIComponent(selectedStream.url)}`;
            sendCommand('CHANGE_SOURCE', { url: proxiedStreamUrl });
            setCurrentStreamIndex(streamIndex);
            setShowQualityModal(false);
        }
    }, [availableStreams]);

    const switchSubtitle = useCallback((subtitleIndex) => {
        sendCommand('TOGGLE_SUBTITLES', { index: subtitleIndex });
        setCurrentSubtitleIndex(subtitleIndex);
        setShowSubtitleModal(false);
    }, []);

    // TV Show specific functions
    const handleVideoEnded = useCallback(async () => {
        // Mark current content as completed
        try {
            await WatchHistoryService.markAsCompleted(
                item,
                mediaType,
                route.params.season,
                route.params.episode
            );
        } catch (error) {
            console.error('Error marking content as completed:', error);
        }

        if (mediaType === 'tv' && nextEpisode) {
            setShowAutoNextModal(true);
            setAutoNextCountdown(15);

            // Start countdown timer
            const countdownInterval = setInterval(() => {
                setAutoNextCountdown(prev => {
                    if (prev <= 1) {
                        clearInterval(countdownInterval);
                        playNextEpisode();
                        return 0;
                    }
                    return prev - 1;
                });
            }, 1000);

            // Store interval reference for cleanup
            setTimeout(() => {
                clearInterval(countdownInterval);
            }, 15000);
        }
    }, [mediaType, nextEpisode, item, route.params]);

    const playNextEpisode = useCallback(() => {
        if (nextEpisode) {
            setShowAutoNextModal(false);
            const nextSeason = nextEpisode.season || route.params.season;
            navigation.replace('Player', {
                item: item,
                mediaType: 'tv',
                season: nextSeason,
                episode: nextEpisode
            });
        }
    }, [nextEpisode, route.params.season, item, navigation]);

    const playEpisode = useCallback((episode, season = null) => {
        const targetSeason = season || route.params.season;
        navigation.replace('Player', {
            item: item,
            mediaType: 'tv',
            season: targetSeason,
            episode: episode
        });
    }, [route.params.season, item, navigation]);

    const loadSeasonEpisodes = useCallback(async (season) => {
        try {
            const seasonDetails = await tmdbApi.getTVSeasonDetails(item.id, season.season_number);
            const episodes = seasonDetails.episodes || [];
            setCurrentSeasonEpisodes(episodes);
            setSelectedSeasonForList(season);
        } catch (error) {
            console.error('Error loading season episodes:', error);
            setCurrentSeasonEpisodes([]);
        }
    }, [item.id]);

    const handleSeasonSelect = useCallback((season) => {
        setShowSeasonModal(false);
        loadSeasonEpisodes(season);
    }, [loadSeasonEpisodes]);

    // Load saved progress when video is ready
    useEffect(() => {
        const loadSavedProgress = async () => {
            if (isPlayerReady && duration > 0 && !hasLoadedSavedProgress) {
                try {
                    const savedProgress = await WatchHistoryService.getProgress(
                        item,
                        mediaType,
                        route.params.season,
                        route.params.episode
                    );

                    if (savedProgress && savedProgress.currentTime > 30) {
                        // Resume from saved position if more than 30 seconds
                        sendCommand('SEEK', { time: savedProgress.currentTime });
                        setCurrentTime(savedProgress.currentTime);
                    }

                    setHasLoadedSavedProgress(true);
                } catch (error) {
                    console.error('Error loading saved progress:', error);
                    setHasLoadedSavedProgress(true);
                }
            }
        };

        loadSavedProgress();
    }, [isPlayerReady, duration, hasLoadedSavedProgress, item, mediaType, route.params]);

    // Save progress periodically and on pause
    useEffect(() => {
        const saveProgress = async () => {
            if (duration > 0 && currentTime > 0 && Math.abs(currentTime - lastSavedTime.current) > 10) {
                try {
                    await WatchHistoryService.saveProgress(
                        item,
                        mediaType,
                        currentTime,
                        duration,
                        route.params.season,
                        route.params.episode
                    );
                    lastSavedTime.current = currentTime;
                } catch (error) {
                    console.error('Error saving progress:', error);
                }
            }
        };

        if (isPlaying && duration > 0) {
            // Save progress every 15 seconds while playing
            progressSaveInterval.current = setInterval(saveProgress, 15000);
        } else {
            // Save immediately when paused
            if (progressSaveInterval.current) {
                clearInterval(progressSaveInterval.current);
                progressSaveInterval.current = null;
            }
            if (!isPlaying && currentTime > 0) {
                saveProgress();
            }
        }

        return () => {
            if (progressSaveInterval.current) {
                clearInterval(progressSaveInterval.current);
                progressSaveInterval.current = null;
            }
        };
    }, [isPlaying, currentTime, duration, item, mediaType, route.params]);

    // Save progress on component unmount or navigation away
    useEffect(() => {
        return () => {
            // Save final progress when component unmounts
            if (duration > 0 && currentTime > 0) {
                WatchHistoryService.saveProgress(
                    item,
                    mediaType,
                    currentTime,
                    duration,
                    route.params.season,
                    route.params.episode
                ).catch(error => console.error('Error saving final progress:', error));
            }
        };
    }, [currentTime, duration, item, mediaType, route.params]);

    const handleMessage = (event) => {
        try {
            const msg = JSON.parse(event.nativeEvent.data);
            switch (msg.type) {
                case 'LOADED':
                    if (!isPlayerReady) setIsPlayerReady(true);
                    setIsVideoLoading(false);
                    setDuration(msg.payload.duration);
                    showControls();
                    break;
                case 'PLAYING':
                    if (!isPlayerReady) setIsPlayerReady(true);
                    setIsVideoLoading(false);
                    setIsPlaying(true);
                    showControls();
                    break;
                case 'PAUSED':
                    setIsPlaying(false);
                    showControls(false);
                    break;
                case 'BUFFERING':
                    setIsVideoLoading(true);
                    break;
                case 'STATUS_UPDATE':
                    setCurrentTime(msg.payload.currentTime);
                    break;
                case 'VIDEO_ENDED':
                    handleVideoEnded();
                    break;
                case 'ERROR':
                    setError(msg.payload.error);
                    Alert.alert("Video Playback Error", msg.payload.error);
                    break;
                case 'DEBUG':
                    console.log('[WebView DEBUG]', msg.payload.message, msg.payload.data || '');
                    break;
            }
        } catch (e) {
            console.error('Failed to parse message from WebView', e);
        }
    };

    if (!playerHtml || error) {
        return (
            <View style={slothStyles.playerLoadingContainer}>
                {error ? <Text style={slothStyles.errorText}>{error}</Text> : <ActivityIndicator size="large" color={SLOTH_COLORS.primary} />}
            </View>
        );
    }

    return (
        <View style={slothStyles.playerContainer}>
            <WebView
                ref={webviewRef}
                style={slothStyles.playerWebView}
                source={{ html: playerHtml, baseUrl: '' }}
                onMessage={handleMessage}
                onError={(e) => setError(e.nativeEvent.description)}
                allowsFullscreenVideo
                mediaPlaybackRequiresUserAction={false}
                originWhitelist={['*']}
            />

            {!isPlayerReady && (
                <View style={slothStyles.playerLoadingContainer}>
                    <ActivityIndicator size="large" color={SLOTH_COLORS.primary} />
                </View>
            )}

            {isPlayerReady && (
                <TouchableOpacity style={StyleSheet.absoluteFill} activeOpacity={1} onPress={handleScreenTap}>
                    {controlsVisible && (
                        <Animated.View style={[slothStyles.controlsOverlay, { opacity: controlsOpacity }]}>
                            <View style={[slothStyles.topControls, { marginTop: insets.top }]}>
                               <TouchableOpacity style={slothStyles.iconButton} onPress={() => navigation.goBack()}><Ionicons name="arrow-back" size={26} color="#FFF" /></TouchableOpacity>
                               <View style={slothStyles.topRightControls}>
                                   {mediaType === 'tv' && currentSeasonEpisodes.length > 0 && (
                                       <TouchableOpacity style={slothStyles.iconButton} onPress={() => setShowEpisodeListModal(true)}>
                                           <Ionicons name="list" size={24} color="#FFF" />
                                       </TouchableOpacity>
                                   )}
                                   {availableStreams.length > 1 && (
                                       <TouchableOpacity style={slothStyles.iconButton} onPress={() => setShowQualityModal(true)}>
                                           <Ionicons name="settings" size={24} color="#FFF" />
                                       </TouchableOpacity>
                                   )}
                                   {availableSubtitles.length > 0 && (
                                       <TouchableOpacity style={slothStyles.iconButton} onPress={() => setShowSubtitleModal(true)}>
                                           <Ionicons name="chatbox" size={24} color="#FFF" />
                                       </TouchableOpacity>
                                   )}
                               </View>
                            </View>
                            <View style={slothStyles.middleControls}>
                                {!isVideoLoading && <TouchableOpacity onPress={() => sendCommand('SEEK_BY', { amount: -10 })} style={slothStyles.seekButton}><Ionicons name="play-back" size={32} color="#FFF" /></TouchableOpacity>}
                                {isVideoLoading ? (
                                    <ActivityIndicator size="large" color="#FFF"/>
                                ) : (
                                    <TouchableOpacity onPress={() => sendCommand(isPlaying ? 'PAUSE' : 'PLAY')}><Ionicons name={isPlaying ? 'pause-circle' : 'play-circle'} size={70} color="#FFF" /></TouchableOpacity>
                                )}
                                {!isVideoLoading && <TouchableOpacity onPress={() => sendCommand('SEEK_BY', { amount: 10 })} style={slothStyles.seekButton}><Ionicons name="play-forward" size={32} color="#FFF" /></TouchableOpacity>}
                            </View>
                            <View style={[slothStyles.bottomControls, { marginBottom: insets.bottom || 10 }]}>
                               <View style={slothStyles.timeRow}><Text style={slothStyles.timeText}>{formatTime(currentTime)}</Text><Text style={slothStyles.timeText}>{formatTime(duration)}</Text></View>
                               <Slider
                                   style={{width: '100%', height: 40}}
                                   value={currentTime}
                                   maximumValue={duration || 1}
                                   onSlidingComplete={(value) => sendCommand('SEEK', {time: value})}
                                   minimumTrackTintColor={SLOTH_COLORS.primary}
                                   maximumTrackTintColor="rgba(255,255,255,0.5)"
                                   thumbTintColor={SLOTH_COLORS.primary}
                               />
                               {mediaType === 'tv' && nextEpisode && !isVideoLoading && (
                                   <TouchableOpacity onPress={playNextEpisode} style={slothStyles.nextEpisodeButtonBottom}>
                                       <Ionicons name="play-skip-forward" size={20} color="#FFF" />
                                       <Text style={slothStyles.nextEpisodeTextBottom}>Next Episode</Text>
                                   </TouchableOpacity>
                               )}
                            </View>
                        </Animated.View>
                    )}
                </TouchableOpacity>
            )}

            <Modal animationType="slide" transparent={true} visible={showQualityModal} onRequestClose={() => setShowQualityModal(false)}>
                <TouchableOpacity style={slothStyles.modalOverlay} activeOpacity={1} onPress={() => setShowQualityModal(false)}>
                    <View style={[slothStyles.modalContent, { marginBottom: insets.bottom }]}>
                        <Text style={slothStyles.modalHeader}>Select Quality</Text>
                        <FlatList
                            data={availableStreams}
                            keyExtractor={(_, index) => index.toString()}
                            renderItem={({ item: stream, index }) => (
                                <TouchableOpacity
                                    style={[slothStyles.modalItem, currentStreamIndex === index && { backgroundColor: 'rgba(94, 56, 244, 0.2)' }]}
                                    onPress={() => switchQuality(index)}
                                >
                                    <Text style={[slothStyles.modalItemText, currentStreamIndex === index && { color: SLOTH_COLORS.primary }]}>
                                        {stream.name || stream.quality || 'Unknown Quality'}
                                    </Text>
                                </TouchableOpacity>
                            )}
                        />
                    </View>
                </TouchableOpacity>
            </Modal>

            <Modal animationType="slide" transparent={true} visible={showSubtitleModal} onRequestClose={() => setShowSubtitleModal(false)}>
                <TouchableOpacity style={slothStyles.modalOverlay} activeOpacity={1} onPress={() => setShowSubtitleModal(false)}>
                    <View style={[slothStyles.modalContent, { marginBottom: insets.bottom }]}>
                        <Text style={slothStyles.modalHeader}>Select Subtitles</Text>
                        <FlatList
                            data={[{ label: 'Off', lang: 'off' }, ...availableSubtitles]}
                            keyExtractor={(_, index) => index.toString()}
                            renderItem={({ item: subtitle, index }) => {
                                const subtitleIndex = index - 1;
                                return (
                                    <TouchableOpacity
                                        style={[slothStyles.modalItem, currentSubtitleIndex === subtitleIndex && { backgroundColor: 'rgba(94, 56, 244, 0.2)' }]}
                                        onPress={() => switchSubtitle(subtitleIndex)}
                                    >
                                        <Text style={[slothStyles.modalItemText, currentSubtitleIndex === subtitleIndex && { color: SLOTH_COLORS.primary }]}>
                                            {subtitle.label || subtitle.lang || `Subtitle ${index}`}
                                        </Text>
                                    </TouchableOpacity>
                                );
                            }}
                        />
                    </View>
                </TouchableOpacity>
            </Modal>

            {/* Episode List Full-Screen Overlay */}
            <Modal animationType="fade" transparent={true} visible={showEpisodeListModal} onRequestClose={() => setShowEpisodeListModal(false)}>
                <View style={slothStyles.episodeOverlayContainer}>
                    {/* Header */}
                    <View style={[slothStyles.episodeOverlayHeader, { paddingTop: insets.top + 10 }]}>
                        <TouchableOpacity
                            style={slothStyles.episodeOverlayCloseButton}
                            onPress={() => setShowEpisodeListModal(false)}
                        >
                            <Ionicons name="close" size={28} color="#FFF" />
                        </TouchableOpacity>
                        <View style={slothStyles.episodeOverlayHeaderContent}>
                            <Text style={slothStyles.episodeOverlayTitle}>
                                {item.name || item.title}
                            </Text>
                            <View style={slothStyles.episodeOverlaySeasonSelector}>
                                <Text style={slothStyles.episodeOverlaySeasonText}>
                                    {selectedSeasonForList?.name || `Season ${selectedSeasonForList?.season_number}`}
                                </Text>
                                {allSeasons.length > 1 && (
                                    <TouchableOpacity
                                        style={slothStyles.episodeOverlaySeasonButton}
                                        onPress={() => setShowSeasonModal(true)}
                                    >
                                        <Ionicons name="chevron-down" size={20} color="#FFF" />
                                    </TouchableOpacity>
                                )}
                            </View>
                        </View>
                    </View>

                    {/* Episodes Grid */}
                    <FlatList
                        data={currentSeasonEpisodes}
                        keyExtractor={(episode) => `episode-${episode.id}`}
                        numColumns={1}
                        contentContainerStyle={[slothStyles.episodeOverlayContent, { paddingBottom: insets.bottom + 20 }]}
                        renderItem={({ item: episode }) => (
                            <TouchableOpacity
                                style={[
                                    slothStyles.episodeOverlayItem,
                                    episode.episode_number === route.params.episode?.episode_number &&
                                    selectedSeasonForList?.season_number === route.params.season?.season_number &&
                                    slothStyles.episodeOverlayItemActive
                                ]}
                                onPress={() => {
                                    setShowEpisodeListModal(false);
                                    if (episode.episode_number !== route.params.episode?.episode_number ||
                                        selectedSeasonForList?.season_number !== route.params.season?.season_number) {
                                        playEpisode(episode, selectedSeasonForList);
                                    }
                                }}
                            >
                                <View style={slothStyles.episodeOverlayImageContainer}>
                                    <View style={slothStyles.episodeOverlayImage}>
                                        <Text style={slothStyles.episodeOverlayImageText}>
                                            {episode.episode_number}
                                        </Text>
                                        {episode.episode_number === route.params.episode?.episode_number &&
                                         selectedSeasonForList?.season_number === route.params.season?.season_number && (
                                            <View style={slothStyles.episodeOverlayPlayIndicator}>
                                                <Ionicons name="play" size={20} color="#FFF" />
                                            </View>
                                        )}
                                    </View>
                                </View>
                                <View style={slothStyles.episodeOverlayInfo}>
                                    <Text style={slothStyles.episodeOverlayEpisodeTitle} numberOfLines={2}>
                                        {episode.episode_number}. {episode.name}
                                    </Text>
                                    <Text style={slothStyles.episodeOverlayRuntime}>
                                        {episode.runtime ? `${episode.runtime} min` : ''}
                                        {episode.air_date && ` • ${new Date(episode.air_date).getFullYear()}`}
                                    </Text>
                                    {episode.overview && (
                                        <Text style={slothStyles.episodeOverlayDescription} numberOfLines={3}>
                                            {episode.overview}
                                        </Text>
                                    )}
                                </View>
                            </TouchableOpacity>
                        )}
                        showsVerticalScrollIndicator={false}
                    />
                </View>
            </Modal>

            {/* Season Selection Modal */}
            <Modal animationType="slide" transparent={true} visible={showSeasonModal} onRequestClose={() => setShowSeasonModal(false)}>
                <TouchableOpacity style={slothStyles.modalOverlay} activeOpacity={1} onPress={() => setShowSeasonModal(false)}>
                    <View style={[slothStyles.modalContent, { marginBottom: insets.bottom }]}>
                        <Text style={slothStyles.modalHeader}>Select Season</Text>
                        <FlatList
                            data={allSeasons}
                            keyExtractor={(season) => `season-${season.season_number}`}
                            renderItem={({ item: season }) => (
                                <TouchableOpacity
                                    style={[
                                        slothStyles.modalItem,
                                        selectedSeasonForList?.season_number === season.season_number && { backgroundColor: 'rgba(94, 56, 244, 0.2)' }
                                    ]}
                                    onPress={() => handleSeasonSelect(season)}
                                >
                                    <Text style={[
                                        slothStyles.modalItemText,
                                        selectedSeasonForList?.season_number === season.season_number && { color: SLOTH_COLORS.primary }
                                    ]}>
                                        {season.name}
                                    </Text>
                                </TouchableOpacity>
                            )}
                        />
                    </View>
                </TouchableOpacity>
            </Modal>

            {/* Auto-Next Episode Modal */}
            <Modal animationType="fade" transparent={true} visible={showAutoNextModal} onRequestClose={() => setShowAutoNextModal(false)}>
                <View style={slothStyles.autoNextOverlay}>
                    <View style={slothStyles.autoNextContainer}>
                        <View style={slothStyles.autoNextContent}>
                            <View style={slothStyles.autoNextImageContainer}>
                                <View style={slothStyles.autoNextImage}>
                                    <Ionicons name="play" size={24} color="#FFF" />
                                </View>
                            </View>
                            <View style={slothStyles.autoNextInfo}>
                                <Text style={slothStyles.autoNextTitle}>Next Episode</Text>
                                <Text style={slothStyles.autoNextEpisodeTitle} numberOfLines={1}>
                                    {nextEpisode?.episode_number}. {nextEpisode?.name}
                                </Text>
                                <Text style={slothStyles.autoNextCountdown}>
                                    Playing in {autoNextCountdown} seconds
                                </Text>
                            </View>
                        </View>
                        <View style={slothStyles.autoNextButtons}>
                            <TouchableOpacity
                                style={slothStyles.autoNextCancelButton}
                                onPress={() => setShowAutoNextModal(false)}
                            >
                                <Text style={slothStyles.autoNextCancelText}>Cancel</Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                                style={slothStyles.autoNextPlayButton}
                                onPress={playNextEpisode}
                            >
                                <Ionicons name="play" size={16} color="#FFF" />
                                <Text style={slothStyles.autoNextPlayText}>Play Now</Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </Modal>
        </View>
    );
};

const slothStyles = StyleSheet.create({
    ...baseSlothStyles,
    playerContainer: { flex: 1, backgroundColor: '#000' },
    playerLoadingContainer: { ...StyleSheet.absoluteFillObject, backgroundColor: '#000', justifyContent: 'center', alignItems: 'center', zIndex: 10 },
    playerWebView: { flex: 1, backgroundColor: '#000' },
    controlsOverlay: {
        ...StyleSheet.absoluteFillObject,
        backgroundColor: 'rgba(0,0,0,0.3)',
        justifyContent: 'space-between',
        zIndex: 5
    },
    topControls: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 20,
        paddingVertical: 10,
        minHeight: 60
    },
    topRightControls: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    middleControls: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-evenly',
        paddingHorizontal: 40
    },
    bottomControls: {
        paddingHorizontal: 20,
        paddingVertical: 10,
        minHeight: 80
    },
    iconButton: {
        padding: 12,
        borderRadius: 25,
        backgroundColor: 'rgba(0,0,0,0.6)',
        marginLeft: 8,
        minWidth: 48,
        minHeight: 48,
        justifyContent: 'center',
        alignItems: 'center'
    },
    seekButton: {
        padding: 20,
        minWidth: 60,
        minHeight: 60,
        justifyContent: 'center',
        alignItems: 'center'
    },
    timeRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 5,
        paddingHorizontal: 4
    },
    timeText: {
        color: '#FFF',
        fontSize: 13,
        fontWeight: 'bold',
        textShadowColor: 'rgba(0,0,0,0.8)',
        textShadowOffset: { width: 1, height: 1 },
        textShadowRadius: 3
    },
    errorText: { color: SLOTH_COLORS.primary, padding: 20, textAlign: 'center' },
    modalOverlay: { flex: 1, justifyContent: 'flex-end', backgroundColor: 'rgba(0,0,0,0.6)' },
    modalContent: { backgroundColor: '#212121', borderTopRightRadius: 20, borderTopLeftRadius: 20, padding: 20 },
    modalHeader: { color: '#FFF', fontSize: 18, fontWeight: 'bold', marginBottom: 15, textAlign: 'center' },
    modalItem: { paddingVertical: 15, borderBottomWidth: 1, borderBottomColor: '#424242' },
    modalItemText: { color: '#FFF', fontSize: 16, textAlign: 'center' },

    // Next Episode Button
    nextEpisodeButton: { alignItems: 'center', marginLeft: 20 },
    nextEpisodeText: { color: '#FFF', fontSize: 12, marginTop: 4, fontWeight: 'bold' },
    nextEpisodeButtonBottom: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: 'rgba(255,255,255,0.15)',
        paddingVertical: 10,
        paddingHorizontal: 20,
        borderRadius: 6,
        marginTop: 15,
        alignSelf: 'center',
        borderWidth: 1,
        borderColor: 'rgba(255,255,255,0.2)',
        minHeight: 44
    },
    nextEpisodeTextBottom: {
        color: '#FFF',
        fontSize: 14,
        fontWeight: 'bold',
        marginLeft: 8
    },

    // Episode List Modal
    episodeModalContent: { backgroundColor: '#212121', borderTopRightRadius: 20, borderTopLeftRadius: 20, padding: 20, maxHeight: '80%' },
    episodeModalHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 15 },
    seasonSwitchButton: { flexDirection: 'row', alignItems: 'center', backgroundColor: 'rgba(255,255,255,0.1)', paddingHorizontal: 12, paddingVertical: 6, borderRadius: 15 },
    seasonSwitchText: { color: '#FFF', fontSize: 14, marginRight: 4 },
    episodeListItem: { flexDirection: 'row', paddingVertical: 12, borderBottomWidth: 1, borderBottomColor: '#424242' },
    episodeListImageContainer: { position: 'relative', marginRight: 12 },
    episodeListImage: { width: 60, height: 34, backgroundColor: '#333', borderRadius: 4, justifyContent: 'center', alignItems: 'center' },
    episodeListImagePlaceholder: { color: '#FFF', fontSize: 12, fontWeight: 'bold' },
    currentEpisodeIndicator: { position: 'absolute', top: 0, right: 0, backgroundColor: SLOTH_COLORS.primary, borderRadius: 10, width: 20, height: 20, justifyContent: 'center', alignItems: 'center' },
    episodeListInfo: { flex: 1 },
    episodeListTitle: { color: '#FFF', fontSize: 16, fontWeight: 'bold', marginBottom: 4 },
    episodeListRuntime: { color: '#999', fontSize: 12, marginBottom: 4 },
    episodeListOverview: { color: '#CCC', fontSize: 14, lineHeight: 18 },

    // Episode Overlay (Full-Screen)
    episodeOverlayContainer: {
        flex: 1,
        backgroundColor: '#141414'
    },
    episodeOverlayHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 20,
        paddingBottom: 24,
        borderBottomWidth: 1,
        borderBottomColor: '#333',
        backgroundColor: '#000'
    },
    episodeOverlayCloseButton: {
        padding: 8,
        marginRight: 15
    },
    episodeOverlayHeaderContent: {
        flex: 1
    },
    episodeOverlayTitle: {
        color: '#FFF',
        fontSize: 28,
        fontWeight: '700',
        marginBottom: 8,
        letterSpacing: -0.5
    },
    episodeOverlaySeasonSelector: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: 'rgba(255,255,255,0.1)',
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 4,
        alignSelf: 'flex-start'
    },
    episodeOverlaySeasonText: {
        color: '#FFF',
        fontSize: 16,
        fontWeight: '500',
        marginRight: 8
    },
    episodeOverlaySeasonButton: {
        padding: 4
    },
    episodeOverlayContent: {
        paddingHorizontal: 20,
        paddingTop: 20
    },
    episodeOverlayItem: {
        flexDirection: 'row',
        marginBottom: 20,
        backgroundColor: 'rgba(255,255,255,0.05)',
        borderRadius: 4,
        padding: 16,
        borderWidth: 2,
        borderColor: 'transparent',
        transition: 'all 0.2s ease'
    },
    episodeOverlayItemActive: {
        borderColor: '#E50914',
        backgroundColor: 'rgba(229, 9, 20, 0.15)'
    },
    episodeOverlayImageContainer: {
        marginRight: 16
    },
    episodeOverlayImage: {
        width: 120,
        height: 68,
        backgroundColor: '#333',
        borderRadius: 6,
        justifyContent: 'center',
        alignItems: 'center',
        position: 'relative'
    },
    episodeOverlayImageText: {
        color: '#FFF',
        fontSize: 18,
        fontWeight: 'bold'
    },
    episodeOverlayPlayIndicator: {
        position: 'absolute',
        top: 4,
        right: 4,
        backgroundColor: '#E50914',
        borderRadius: 12,
        width: 24,
        height: 24,
        justifyContent: 'center',
        alignItems: 'center'
    },
    episodeOverlayInfo: {
        flex: 1,
        justifyContent: 'center'
    },
    episodeOverlayEpisodeTitle: {
        color: '#FFF',
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 6,
        lineHeight: 24
    },
    episodeOverlayRuntime: {
        color: '#999',
        fontSize: 14,
        marginBottom: 8
    },
    episodeOverlayDescription: {
        color: '#CCC',
        fontSize: 15,
        lineHeight: 20
    },

    // Auto-Next Episode Modal
    autoNextOverlay: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0,0,0,0.85)'
    },
    autoNextContainer: {
        backgroundColor: '#181818',
        borderRadius: 8,
        padding: 24,
        margin: 20,
        maxWidth: 380,
        borderWidth: 1,
        borderColor: '#333'
    },
    autoNextContent: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 24
    },
    autoNextImageContainer: {
        marginRight: 16
    },
    autoNextImage: {
        width: 80,
        height: 45,
        backgroundColor: '#333',
        borderRadius: 4,
        justifyContent: 'center',
        alignItems: 'center'
    },
    autoNextInfo: {
        flex: 1
    },
    autoNextTitle: {
        color: '#B3B3B3',
        fontSize: 14,
        marginBottom: 6,
        textTransform: 'uppercase',
        letterSpacing: 0.5
    },
    autoNextEpisodeTitle: {
        color: '#FFF',
        fontSize: 18,
        fontWeight: '600',
        marginBottom: 8,
        lineHeight: 22
    },
    autoNextCountdown: {
        color: '#E50914',
        fontSize: 15,
        fontWeight: '500'
    },
    autoNextButtons: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        gap: 12
    },
    autoNextCancelButton: {
        flex: 1,
        paddingVertical: 14,
        paddingHorizontal: 20,
        backgroundColor: 'rgba(255,255,255,0.1)',
        borderRadius: 4,
        alignItems: 'center',
        borderWidth: 1,
        borderColor: 'rgba(255,255,255,0.2)'
    },
    autoNextCancelText: {
        color: '#FFF',
        fontSize: 16,
        fontWeight: '600'
    },
    autoNextPlayButton: {
        flex: 1,
        paddingVertical: 14,
        paddingHorizontal: 20,
        backgroundColor: '#E50914',
        borderRadius: 4,
        alignItems: 'center',
        flexDirection: 'row',
        justifyContent: 'center'
    },
    autoNextPlayText: {
        color: '#FFF',
        fontSize: 16,
        fontWeight: '600',
        marginLeft: 8
    }
});

export default PlayerScreen;