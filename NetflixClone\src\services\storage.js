import AsyncStorage from '@react-native-async-storage/async-storage';
import { STORAGE_KEYS } from '../utils/constants';

class StorageService {
  // Watch History Management
  async getWatchHistory() {
    try {
      const history = await AsyncStorage.getItem(STORAGE_KEYS.WATCH_HISTORY);
      return history ? JSON.parse(history) : [];
    } catch (error) {
      console.error('Error getting watch history:', error);
      return [];
    }
  }

  async addToWatchHistory(item) {
    try {
      const history = await this.getWatchHistory();
      
      // Remove existing entry if it exists
      const filteredHistory = history.filter(h => 
        !(h.id === item.id && h.type === item.type)
      );

      // Add new entry at the beginning
      const newHistory = [
        {
          ...item,
          lastWatched: new Date().toISOString(),
        },
        ...filteredHistory
      ];

      // Keep only last 50 items
      const trimmedHistory = newHistory.slice(0, 50);

      await AsyncStorage.setItem(
        STORAGE_KEYS.WATCH_HISTORY, 
        JSON.stringify(trimmedHistory)
      );

      return trimmedHistory;
    } catch (error) {
      console.error('Error adding to watch history:', error);
      throw error;
    }
  }

  async updateWatchProgress(id, type, progress, duration = null) {
    try {
      const history = await this.getWatchHistory();
      const existingIndex = history.findIndex(h => h.id === id && h.type === type);

      if (existingIndex !== -1) {
        // Update existing entry
        history[existingIndex] = {
          ...history[existingIndex],
          progress,
          duration: duration || history[existingIndex].duration,
          lastWatched: new Date().toISOString(),
        };
      } else {
        // This shouldn't happen, but handle it gracefully
        console.warn('Trying to update progress for non-existent item');
        return history;
      }

      await AsyncStorage.setItem(
        STORAGE_KEYS.WATCH_HISTORY, 
        JSON.stringify(history)
      );

      return history;
    } catch (error) {
      console.error('Error updating watch progress:', error);
      throw error;
    }
  }

  async removeFromWatchHistory(id, type) {
    try {
      const history = await this.getWatchHistory();
      const filteredHistory = history.filter(h => 
        !(h.id === id && h.type === type)
      );

      await AsyncStorage.setItem(
        STORAGE_KEYS.WATCH_HISTORY, 
        JSON.stringify(filteredHistory)
      );

      return filteredHistory;
    } catch (error) {
      console.error('Error removing from watch history:', error);
      throw error;
    }
  }

  async clearWatchHistory() {
    try {
      await AsyncStorage.removeItem(STORAGE_KEYS.WATCH_HISTORY);
      return [];
    } catch (error) {
      console.error('Error clearing watch history:', error);
      throw error;
    }
  }

  // User Preferences Management
  async getUserPreferences() {
    try {
      const preferences = await AsyncStorage.getItem(STORAGE_KEYS.USER_PREFERENCES);
      return preferences ? JSON.parse(preferences) : {
        autoplay: true,
        subtitles: false,
        quality: 'auto',
        volume: 1.0,
      };
    } catch (error) {
      console.error('Error getting user preferences:', error);
      return {
        autoplay: true,
        subtitles: false,
        quality: 'auto',
        volume: 1.0,
      };
    }
  }

  async updateUserPreferences(preferences) {
    try {
      const currentPreferences = await this.getUserPreferences();
      const updatedPreferences = { ...currentPreferences, ...preferences };

      await AsyncStorage.setItem(
        STORAGE_KEYS.USER_PREFERENCES, 
        JSON.stringify(updatedPreferences)
      );

      return updatedPreferences;
    } catch (error) {
      console.error('Error updating user preferences:', error);
      throw error;
    }
  }

  // Helper method to get watch progress for a specific item
  async getWatchProgress(id, type) {
    try {
      const history = await this.getWatchHistory();
      const item = history.find(h => h.id === id && h.type === type);
      return item ? item.progress : 0;
    } catch (error) {
      console.error('Error getting watch progress:', error);
      return 0;
    }
  }

  // Helper method to check if item is in continue watching (>5% and <90% watched)
  async getContinueWatching() {
    try {
      const history = await this.getWatchHistory();
      return history.filter(item => 
        item.progress > 0.05 && item.progress < 0.9
      ).slice(0, 10); // Return top 10 continue watching items
    } catch (error) {
      console.error('Error getting continue watching:', error);
      return [];
    }
  }
}

export default new StorageService();
