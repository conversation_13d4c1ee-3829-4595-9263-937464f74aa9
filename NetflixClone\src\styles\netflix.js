import { StyleSheet, Dimensions } from 'react-native';
import { NETFLIX_COLORS } from '../utils/constants';

const { width, height } = Dimensions.get('window');

export const netflixStyles = StyleSheet.create({
  // Container Styles
  container: {
    flex: 1,
    backgroundColor: NETFLIX_COLORS.black,
  },

  safeArea: {
    flex: 1,
    backgroundColor: NETFLIX_COLORS.black,
    paddingBottom: 0, // Remove bottom padding to make navigation bar black
  },

  scrollContainer: {
    flexGrow: 1,
    backgroundColor: NETFLIX_COLORS.black,
  },

  // Header Styles
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: NETFLIX_COLORS.black,
    zIndex: 1000,
    elevation: 5, // Android shadow
    shadowColor: '#000', // iOS shadow
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },

  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: NETFLIX_COLORS.primary,
  },

  // Hero Banner Styles
  heroBanner: {
    width: width,
    height: height * 0.75, // Increased height for better Netflix look
    position: 'relative',
  },

  heroImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },

  heroGradient: {
    flex: 1,
    justifyContent: 'flex-end',
  },

  heroContent: {
    paddingHorizontal: 20,
    paddingBottom: 40,
    alignItems: 'flex-start',
  },

  heroBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },

  heroBadgeText: {
    backgroundColor: NETFLIX_COLORS.primary,
    color: NETFLIX_COLORS.white,
    fontSize: 14,
    fontWeight: 'bold',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 3,
    marginRight: 8,
  },

  heroOriginalText: {
    color: NETFLIX_COLORS.lightGray,
    fontSize: 12,
    fontWeight: '600',
    letterSpacing: 1,
  },

  heroTitle: {
    fontSize: 36,
    fontWeight: 'bold',
    color: NETFLIX_COLORS.white,
    marginBottom: 12,
    lineHeight: 40,
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },

  heroMetadata: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    flexWrap: 'wrap',
  },

  heroYear: {
    color: NETFLIX_COLORS.white,
    fontSize: 14,
    fontWeight: '600',
    marginRight: 15,
  },

  heroSeasons: {
    color: NETFLIX_COLORS.white,
    fontSize: 14,
    fontWeight: '600',
    marginRight: 15,
  },

  heroGenre: {
    backgroundColor: NETFLIX_COLORS.mediumGray,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 3,
    marginRight: 10,
  },

  heroGenreText: {
    color: NETFLIX_COLORS.white,
    fontSize: 12,
    fontWeight: '600',
  },

  heroRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  heroRatingText: {
    color: NETFLIX_COLORS.white,
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 4,
  },

  heroRanking: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },

  heroRankingText: {
    color: NETFLIX_COLORS.white,
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },

  heroOverview: {
    fontSize: 15,
    color: NETFLIX_COLORS.lightGray,
    lineHeight: 20,
    marginBottom: 25,
    maxWidth: '90%',
  },

  heroButtons: {
    flexDirection: 'row',
    gap: 12,
  },

  // Button Styles
  playButton: {
    backgroundColor: NETFLIX_COLORS.white,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 6,
    marginBottom: 10,
  },

  playButtonText: {
    color: NETFLIX_COLORS.black,
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },

  infoButton: {
    backgroundColor: 'rgba(109, 109, 110, 0.7)',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 6,
  },

  infoButtonText: {
    color: NETFLIX_COLORS.white,
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },

  // Media Row Styles
  mediaRow: {
    marginVertical: 10,
  },

  mediaRowTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: NETFLIX_COLORS.white,
    marginLeft: 20,
    marginBottom: 10,
  },

  mediaRowScroll: {
    paddingLeft: 20,
  },

  // Media Card Styles
  mediaCard: {
    width: 120,
    marginRight: 10,
  },

  mediaCardLarge: {
    width: 160,
    marginRight: 10,
  },

  mediaCardImage: {
    width: '100%',
    height: 180,
    borderRadius: 6,
    resizeMode: 'cover',
  },

  mediaCardImageLarge: {
    width: '100%',
    height: 240,
    borderRadius: 6,
    resizeMode: 'cover',
  },

  mediaCardTitle: {
    fontSize: 12,
    color: NETFLIX_COLORS.white,
    marginTop: 5,
    textAlign: 'center',
  },

  mediaCardTitleLarge: {
    fontSize: 14,
    color: NETFLIX_COLORS.white,
    marginTop: 5,
    textAlign: 'center',
  },

  // Progress Bar Styles
  progressContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderBottomLeftRadius: 6,
    borderBottomRightRadius: 6,
  },

  progressBar: {
    height: '100%',
    backgroundColor: NETFLIX_COLORS.primary,
    borderBottomLeftRadius: 6,
    borderBottomRightRadius: 6,
  },

  // Search Styles
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: NETFLIX_COLORS.darkGray,
    marginHorizontal: 20,
    marginVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 8,
  },

  searchInput: {
    flex: 1,
    color: NETFLIX_COLORS.white,
    fontSize: 16,
    paddingVertical: 12,
    marginLeft: 10,
  },

  // Loading Styles
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: NETFLIX_COLORS.black,
  },

  loadingText: {
    color: NETFLIX_COLORS.white,
    fontSize: 16,
    marginTop: 10,
  },

  // Error Styles
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: NETFLIX_COLORS.black,
    paddingHorizontal: 20,
  },

  errorText: {
    color: NETFLIX_COLORS.white,
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 20,
  },

  retryButton: {
    backgroundColor: NETFLIX_COLORS.primary,
    paddingVertical: 12,
    paddingHorizontal: 30,
    borderRadius: 6,
  },

  retryButtonText: {
    color: NETFLIX_COLORS.white,
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export const animations = {
  fadeIn: {
    from: { opacity: 0 },
    to: { opacity: 1 },
  },
  slideUp: {
    from: { transform: [{ translateY: 50 }], opacity: 0 },
    to: { transform: [{ translateY: 0 }], opacity: 1 },
  },
  scale: {
    from: { transform: [{ scale: 0.9 }], opacity: 0 },
    to: { transform: [{ scale: 1 }], opacity: 1 },
  },
};
