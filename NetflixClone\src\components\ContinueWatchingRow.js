import React, { memo, useCallback } from 'react';
import {
  View,
  Text,
  FlatList,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { slothStyles, SLOTH_COLORS } from '../styles/sloth';
import MediaCard from './MediaCard';
import WatchHistoryService from '../services/WatchHistoryService';

const ContinueWatchingRow = memo(({
  title = "Continue Watching",
  data,
  onItemPress,
  loading = false,
  onDataChange, // Callback when data changes (for refreshing)
}) => {
  const handleRemoveItem = useCallback(async (item) => {
    Alert.alert(
      "Remove from Continue Watching",
      `Remove "${item.title}" from your continue watching list?`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Remove",
          style: "destructive",
          onPress: async () => {
            try {
              await WatchHistoryService.removeFromContinueWatching(
                item,
                item.mediaType,
                item.season,
                item.episode
              );
              if (onDataChange) {
                onDataChange();
              }
            } catch (error) {
              console.error('Error removing from continue watching:', error);
              Alert.alert('Error', 'Failed to remove item from continue watching');
            }
          }
        }
      ]
    );
  }, [onDataChange]);

  const renderItem = useCallback(({ item }) => {
    const watchProgress = {
      progressPercent: item.progressPercent,
      episode: item.episode,
      season: item.season
    };

    return (
      <MediaCard
        item={item}
        onPress={onItemPress}
        watchProgress={watchProgress}
        showRemoveButton={true}
        onRemove={handleRemoveItem}
      />
    );
  }, [onItemPress, handleRemoveItem]);
  
  const keyExtractor = useCallback((item) => {
    if (item.mediaType === 'tv' && item.season && item.episode) {
      return `${item.id}_s${item.season.season_number}_e${item.episode.episode_number}`;
    }
    return `${item.mediaType}_${item.id}`;
  }, []);

  // Card dimensions for getItemLayout optimization
  const CARD_WIDTH = 140;
  const CARD_MARGIN = 10;
  const CARD_FULL_WIDTH = CARD_WIDTH + CARD_MARGIN;

  if (loading) {
    return (
      <View style={slothStyles.mediaRowContainer}>
        <Text style={slothStyles.mediaRowTitle}>{title}</Text>
        <View style={{ height: 210, justifyContent: 'center', alignItems: 'center' }}>
          <ActivityIndicator size="large" color={SLOTH_COLORS.primary} />
        </View>
      </View>
    );
  }

  if (!data || data.length === 0) {
    return null;
  }

  return (
    <View style={slothStyles.mediaRowContainer}>
      <Text style={slothStyles.mediaRowTitle}>{title}</Text>
      <FlatList
        data={data}
        renderItem={renderItem}
        keyExtractor={keyExtractor}
        horizontal
        showsHorizontalScrollIndicator={false}
        nestedScrollEnabled={true}
        scrollEnabled={true}
        contentContainerStyle={slothStyles.mediaRowScroll}
        removeClippedSubviews={false}
        maxToRenderPerBatch={7}
        windowSize={5}
        initialNumToRender={5}
        scrollEventThrottle={16}
        getItemLayout={(_, index) => ({
          length: CARD_FULL_WIDTH,
          offset: CARD_FULL_WIDTH * index,
          index,
        })}
      />
    </View>
  );
});

export default ContinueWatchingRow;
