import { StyleSheet, Dimensions, Platform } from 'react-native';

const { width, height } = Dimensions.get('window');

export const SLOTH_COLORS = {
  background: '#0F1014',
  dark: '#000',
  primary: '#5E38F4',
  white: '#FFFFFF',
  text: '#E1E1E1',
  textSecondary: 'rgba(235, 235, 245, 0.6)',
  badgeBg: 'rgba(255, 255, 255, 0.15)',
  infoButtonBg: 'rgba(255, 255, 255, 0.2)',
  iconContainerBg: 'rgba(255, 255, 255, 0.1)',
};

export const slothStyles = StyleSheet.create({
  //... All Previous Styles (Home, Search, Detail, Episodes)...
  container: { flex: 1, backgroundColor: SLOTH_COLORS.background, },
  loadingContainer: { flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: SLOTH_COLORS.background, },
  header: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', paddingHorizontal: 15, paddingTop: 10, width: '100%', marginTop: 40, },
  logo: { fontFamily: Platform.OS === 'ios' ? 'Helvetica-Bold' : 'sans-serif-condensed', fontSize: 28, fontWeight: 'bold', color: SLOTH_COLORS.primary, backgroundColor: SLOTH_COLORS.white, borderRadius: 9, width: 38, height: 38, textAlign: 'center', lineHeight: 38, },
  headerIcons: { flexDirection: 'row', alignItems: 'center', },
  iconContainer: { width: 40, height: 40, borderRadius: 20, backgroundColor: SLOTH_COLORS.iconContainerBg, justifyContent: 'center', alignItems: 'center', },
  icon: { fontSize: 22, color: SLOTH_COLORS.white, },
  heroBanner: { width: '100%', justifyContent: 'space-between', },
  heroGradient: { flex: 1, justifyContent: 'space-between', paddingBottom: 20, },
  heroContent: { paddingHorizontal: 20, alignItems: 'center', paddingBottom: 5, },
  heroOriginalsContainer: { flexDirection: 'row', alignItems: 'center', marginBottom: 5, },
  heroTitle: { color: SLOTH_COLORS.white, fontSize: 42, fontWeight: 'bold', textAlign: 'center', marginBottom: 8, textTransform: 'uppercase', },
  heroMetadata: { flexDirection: 'row', alignItems: 'center', marginBottom: 25, },
  metadataText: { color: SLOTH_COLORS.textSecondary, fontSize: 14, fontWeight: '600', marginHorizontal: 8, },
  badge: { backgroundColor: SLOTH_COLORS.badgeBg, borderColor: 'rgba(255, 255, 255, 0.3)', borderWidth: 1, color: SLOTH_COLORS.white, paddingHorizontal: 10, paddingVertical: 3, borderRadius: 15, fontSize: 12, fontWeight: 'bold', marginHorizontal: 8, },
  heroButtons: { flexDirection: 'row', justifyContent: 'center', alignItems: 'center', width: '100%', },
  playButton: { flexDirection: 'row', backgroundColor: SLOTH_COLORS.white, paddingVertical: 12, paddingHorizontal: 35, borderRadius: 30, alignItems: 'center', marginHorizontal: 5, },
  playButtonText: { color: SLOTH_COLORS.dark, fontSize: 16, fontWeight: 'bold', marginLeft: 8, },
  infoButton: { flexDirection: 'row', backgroundColor: SLOTH_COLORS.infoButtonBg, paddingVertical: 12, paddingHorizontal: 35, borderRadius: 30, alignItems: 'center', marginHorizontal: 5, },
  infoButtonText: { color: SLOTH_COLORS.white, fontSize: 16, fontWeight: 'bold', marginLeft: 8, },
  mediaRowContainer: { marginBottom: 25, },
  mediaRowTitle: { color: SLOTH_COLORS.white, fontSize: 20, fontWeight: 'bold', paddingLeft: 15, marginBottom: 15, },
  mediaRowScroll: { paddingLeft: 15, paddingRight: 5, },
  mediaCardContainer: { width: 140, marginRight: 10, },
  mediaCardImage: { width: '100%', height: 210, borderRadius: 12, backgroundColor: '#222', },
  mediaCardLogoOverlay: { position: 'absolute', top: 8, left: 8, backgroundColor: SLOTH_COLORS.white, borderRadius: 5, width: 20, height: 20, justifyContent: 'center', alignItems: 'center', },
  mediaCardLogo: { color: SLOTH_COLORS.primary, fontWeight: 'bold', fontSize: 14, },
  searchGridItem: { flex: 1 / 3, padding: 5, },
  gridCardContainer: { width: '100%', },
  gridCardImage: { width: '100%', aspectRatio: 2 / 3, borderRadius: 8, backgroundColor: '#222', },
  emptyContainer: { flex: 1, justifyContent: 'center', alignItems: 'center', marginTop: '25%', },
  emptyText: { color: SLOTH_COLORS.white, fontSize: 18, fontWeight: 'bold', marginTop: 20, },
  emptySubtext: { color: SLOTH_COLORS.textSecondary, fontSize: 14, marginTop: 8, textAlign: 'center', paddingHorizontal: 40, },
  detailBackdrop: { width: '100%', height: height * 0.4, },
  detailHeader: { position: 'absolute', top: 0, left: 0, right: 0, zIndex: 10, padding: 15, },
  detailContent: { flex: 1, paddingHorizontal: 15, },
  detailPosterInfoBlock: { flexDirection: 'row', marginTop: -80, alignItems: 'flex-end', },
  detailPosterImage: { width: 120, height: 180, borderRadius: 12, backgroundColor: '#222', },
  detailTitleBlock: { flex: 1, marginLeft: 15, justifyContent: 'flex-end', paddingBottom: 5, },
  detailTitle: { fontFamily: Platform.OS === 'ios' ? 'Helvetica-Bold' : 'sans-serif-condensed', color: SLOTH_COLORS.white, fontSize: 24, fontWeight: 'bold', textShadowColor: 'rgba(0, 0, 0, 0.75)', textShadowOffset: { width: -1, height: 1 }, textShadowRadius: 10, },
  detailMetaRow: { flexDirection: 'row', alignItems: 'center', marginTop: 5, },
  detailMetaText: { color: SLOTH_COLORS.textSecondary, fontSize: 14, fontWeight: '600', marginRight: 10, },
  detailStarIcon: { color: SLOTH_COLORS.primary, marginRight: 3, },
  detailMainActions: { marginTop: 20, flexDirection: 'row', alignItems: 'center', },
  detailPlayButton: { flex: 1, flexDirection: 'row', backgroundColor: SLOTH_COLORS.primary, paddingVertical: 14, borderRadius: 10, justifyContent: 'center', alignItems: 'center', },
  detailPlayButtonText: { color: SLOTH_COLORS.white, fontSize: 16, fontWeight: 'bold', marginLeft: 8, },
  detailIconBtn: { padding: 12, backgroundColor: SLOTH_COLORS.infoButtonBg, borderRadius: 10, marginLeft: 10, },
  detailSectionTitle: { color: SLOTH_COLORS.white, fontSize: 18, fontWeight: 'bold', marginTop: 25, marginBottom: 10, },
  detailOverview: { color: SLOTH_COLORS.text, fontSize: 15, lineHeight: 22, },
  episodesScreenContainer: { flex: 1, },
  episodesScreenOverlay: { ...StyleSheet.absoluteFillObject, backgroundColor: 'rgba(15, 16, 20, 0.90)', },
  episodesHeader: { flexDirection: 'row', alignItems: 'center', paddingHorizontal: 15, paddingVertical: 10, },
  episodesHeaderTitle: { color: SLOTH_COLORS.white, fontSize: 20, fontWeight: 'bold', marginLeft: 15, flex: 1, },
  seasonSelector: { flexDirection: 'row', alignItems: 'center', backgroundColor: 'rgba(255,255,255,0.08)', marginHorizontal: 15, paddingVertical: 10, paddingHorizontal: 15, borderRadius: 30, justifyContent: 'space-between', marginBottom: 15, borderWidth: 1, borderColor: 'rgba(255,255,255,0.1)', },
  seasonSelectorText: { color: SLOTH_COLORS.white, fontSize: 16, fontWeight: '600', },
  episodeCardContainer: { paddingHorizontal: 15, paddingBottom: 20, borderBottomWidth: 1, borderBottomColor: 'rgba(255,255,255,0.07)', },
  episodeImageContainer: { width: '100%', aspectRatio: 16 / 9, backgroundColor: '#222', borderRadius: 12, justifyContent: 'center', alignItems: 'center', overflow: 'hidden', },
  episodeImage: { width: '100%', height: '100%', },
  episodePlayIconOverlay: { position: 'absolute', backgroundColor: 'rgba(0,0,0,0.5)', width: 50, height: 50, borderRadius: 25, justifyContent: 'center', alignItems: 'center', },
  episodeInfoBlock: { paddingTop: 15, },
  episodeTitleRow: { flexDirection: 'row', alignItems: 'center', },
  episodeNumber: { color: SLOTH_COLORS.primary, fontSize: 16, fontWeight: 'bold', fontStyle: 'italic', },
  episodeTitle: { color: SLOTH_COLORS.white, fontSize: 18, fontWeight: 'bold', marginLeft: 10, flex: 1, },
  episodeMetaText: { color: SLOTH_COLORS.textSecondary, fontSize: 14, marginTop: 6, },
  episodeOverview: { color: SLOTH_COLORS.text, fontSize: 15, lineHeight: 22, marginTop: 10, },
  episodeReadMoreText: { color: SLOTH_COLORS.primary, fontSize: 15, fontWeight: 'bold', marginTop: 8, },

  // --- CAST SECTION STYLES ---
  castRowContainer: { marginBottom: 25, },
  castRowTitle: { color: SLOTH_COLORS.white, fontSize: 18, fontWeight: 'bold', paddingLeft: 15, marginBottom: 15, },
  castRowScroll: { paddingLeft: 15, paddingRight: 5, },
  castCardContainer: { width: 100, marginRight: 15, alignItems: 'center', },
  castCardImage: { width: 80, height: 80, borderRadius: 40, backgroundColor: '#333', marginBottom: 8, },
  castCardName: { color: SLOTH_COLORS.white, fontSize: 12, fontWeight: 'bold', textAlign: 'center', marginBottom: 2, },
  castCardCharacter: { color: SLOTH_COLORS.textSecondary, fontSize: 11, textAlign: 'center', },

  // --- VIDEO SECTION STYLES ---
  videoRowContainer: { marginBottom: 25, },
  videoRowTitle: { color: SLOTH_COLORS.white, fontSize: 18, fontWeight: 'bold', paddingLeft: 15, marginBottom: 15, },
  videoRowScroll: { paddingLeft: 15, paddingRight: 5, },
  videoCardContainer: { width: 200, marginRight: 15, },
  videoCardThumbnail: { width: '100%', height: 112, borderRadius: 8, backgroundColor: '#333', justifyContent: 'center', alignItems: 'center', overflow: 'hidden', },
  videoCardPlayOverlay: { position: 'absolute', backgroundColor: 'rgba(0,0,0,0.6)', width: 50, height: 50, borderRadius: 25, justifyContent: 'center', alignItems: 'center', },
  videoCardTitle: { color: SLOTH_COLORS.white, fontSize: 13, fontWeight: '600', marginTop: 8, numberOfLines: 2, },

  // --- PLAYER STYLES ---
  playerContainer: {
    flex: 1,
    backgroundColor: '#000',
    justifyContent: 'center'
  },
  playerLoadingContainer: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#000',
    justifyContent: 'center',
    alignItems: 'center',
  },
  playerVideo: {
    ...StyleSheet.absoluteFillObject,
  },
  controlsOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.4)',
    justifyContent: 'space-between',
  },
  topControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingTop: 10,
  },
  topRightControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  title: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 10,
  },
  iconButton: {
    padding: 10,
  },
  centerControls: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  playPauseButton: {
      backgroundColor: 'rgba(0,0,0,0.5)',
      width: 70,
      height: 70,
      borderRadius: 35,
      justifyContent: 'center',
      alignItems: 'center',
  },
  bottomControls: {
    paddingHorizontal: 15,
    paddingBottom: 10,
  },
  timecodeContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 5,
    paddingHorizontal: 5
  },
  timecodeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: '600'
  },

  // --- MODAL STYLES ---
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: SLOTH_COLORS.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    paddingHorizontal: 20,
    maxHeight: height * 0.6,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.1)',
  },
  modalHeader: {
    color: SLOTH_COLORS.white,
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  modalItem: {
    paddingVertical: 15,
    paddingHorizontal: 10,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.05)',
  },
  modalItemText: {
    color: SLOTH_COLORS.white,
    fontSize: 16,
    fontWeight: '500',
  },
});