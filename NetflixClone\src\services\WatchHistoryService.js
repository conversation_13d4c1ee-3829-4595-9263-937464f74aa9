import AsyncStorage from '@react-native-async-storage/async-storage';

const WATCH_HISTORY_KEY = 'watch_history';
const COMPLETION_THRESHOLD = 0.9; // 90% watched is considered complete
const MIN_WATCH_TIME = 30; // Minimum 30 seconds to be considered "started"

class WatchHistoryService {
  constructor() {
    this.watchHistory = new Map();
    this.initialized = false;
  }

  async initialize() {
    if (this.initialized) return;
    
    try {
      const stored = await AsyncStorage.getItem(WATCH_HISTORY_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        this.watchHistory = new Map(Object.entries(parsed));
      }
      this.initialized = true;
    } catch (error) {
      console.error('Error initializing watch history:', error);
      this.watchHistory = new Map();
      this.initialized = true;
    }
  }

  generateWatchKey(item, mediaType, season = null, episode = null) {
    if (mediaType === 'tv' && season && episode) {
      return `tv_${item.id}_s${season.season_number}_e${episode.episode_number}`;
    }
    return `${mediaType}_${item.id}`;
  }

  async saveProgress(item, mediaType, currentTime, duration, season = null, episode = null) {
    await this.initialize();
    
    if (!item || !duration || currentTime < MIN_WATCH_TIME) return;

    const watchKey = this.generateWatchKey(item, mediaType, season, episode);
    const progressPercent = currentTime / duration;
    
    const watchData = {
      id: item.id,
      title: item.title || item.name,
      poster_path: item.poster_path,
      backdrop_path: item.backdrop_path,
      mediaType,
      currentTime,
      duration,
      progressPercent,
      lastWatched: Date.now(),
      completed: progressPercent >= COMPLETION_THRESHOLD,
      // TV show specific data
      ...(mediaType === 'tv' && season && episode && {
        season: {
          season_number: season.season_number,
          name: season.name
        },
        episode: {
          episode_number: episode.episode_number,
          name: episode.name,
          still_path: episode.still_path
        }
      })
    };

    this.watchHistory.set(watchKey, watchData);
    await this.persistToStorage();
  }

  async getProgress(item, mediaType, season = null, episode = null) {
    await this.initialize();
    const watchKey = this.generateWatchKey(item, mediaType, season, episode);
    return this.watchHistory.get(watchKey) || null;
  }

  async getContinueWatchingList() {
    await this.initialize();
    
    const continueWatching = Array.from(this.watchHistory.values())
      .filter(item => !item.completed && item.progressPercent > 0.05) // At least 5% watched
      .sort((a, b) => b.lastWatched - a.lastWatched) // Most recent first
      .slice(0, 20); // Limit to 20 items

    return continueWatching;
  }

  async removeFromContinueWatching(item, mediaType, season = null, episode = null) {
    await this.initialize();
    const watchKey = this.generateWatchKey(item, mediaType, season, episode);
    this.watchHistory.delete(watchKey);
    await this.persistToStorage();
  }

  async markAsCompleted(item, mediaType, season = null, episode = null) {
    await this.initialize();
    const watchKey = this.generateWatchKey(item, mediaType, season, episode);
    const existing = this.watchHistory.get(watchKey);
    
    if (existing) {
      existing.completed = true;
      existing.progressPercent = 1.0;
      existing.lastWatched = Date.now();
      this.watchHistory.set(watchKey, existing);
      await this.persistToStorage();
    }
  }

  async getNextEpisode(item, currentSeason, currentEpisode) {
    // This would need to be implemented with TMDB API calls
    // For now, return null - will be implemented in the PlayerScreen
    return null;
  }

  async clearAllHistory() {
    this.watchHistory.clear();
    await this.persistToStorage();
  }

  async persistToStorage() {
    try {
      const dataToStore = Object.fromEntries(this.watchHistory);
      await AsyncStorage.setItem(WATCH_HISTORY_KEY, JSON.stringify(dataToStore));
    } catch (error) {
      console.error('Error persisting watch history:', error);
    }
  }

  // Helper method to check if content should auto-advance to next episode
  shouldAutoAdvance(progressPercent) {
    return progressPercent >= COMPLETION_THRESHOLD;
  }

  // Helper method to format episode info for display
  formatEpisodeInfo(season, episode) {
    if (!season || !episode) return '';
    return `S${season.season_number}:E${episode.episode_number}`;
  }
}

export default new WatchHistoryService();
