import { TMDB_API_KEY, TMDB_BASE_URL, TMDB_IMAGE_BASE_URL } from '../utils/constants';

class TMDBApi {
  constructor() {
    this.apiKey = TMDB_API_KEY;
    this.baseUrl = TMDB_BASE_URL;
    this.imageBaseUrl = TMDB_IMAGE_BASE_URL;
  }

  async makeRequest(endpoint, params = {}) {
    try {
      const url = new URL(`${this.baseUrl}${endpoint}`);
      url.searchParams.append('api_key', this.apiKey);
      
      Object.keys(params).forEach(key => {
        if (params[key] !== undefined && params[key] !== null) {
          url.searchParams.append(key, params[key]);
        }
      });

      const response = await fetch(url.toString());
      
      if (!response.ok) {
        throw new Error(`TMDB API Error: ${response.status} ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('TMDB API Request failed:', error);
      throw error;
    }
  }

  // Get trending movies/TV shows
  async getTrending(mediaType = 'all', timeWindow = 'day') {
    return this.makeRequest(`/trending/${mediaType}/${timeWindow}`);
  }

  // Search for movies/TV shows
  async search(query, mediaType = 'multi') {
    if (mediaType === 'multi') {
      return this.makeRequest('/search/multi', { query });
    }
    return this.makeRequest(`/search/${mediaType}`, { query });
  }

  // Get movie details
  async getMovieDetails(movieId) {
    return this.makeRequest(`/movie/${movieId}`, {
      append_to_response: 'videos,credits,similar,recommendations'
    });
  }

  // Get TV show details
  async getTVDetails(tvId) {
    return this.makeRequest(`/tv/${tvId}`, {
      append_to_response: 'videos,credits,similar,recommendations'
    });
  }

  // Get popular movies
  async getPopularMovies(page = 1) {
    return this.makeRequest('/movie/popular', { page });
  }

  // Get popular TV shows
  async getPopularTV(page = 1) {
    return this.makeRequest('/tv/popular', { page });
  }

  // Get top rated movies
  async getTopRatedMovies(page = 1) {
    return this.makeRequest('/movie/top_rated', { page });
  }

  // Get top rated TV shows
  async getTopRatedTV(page = 1) {
    return this.makeRequest('/tv/top_rated', { page });
  }

  // Get now playing movies
  async getNowPlayingMovies(page = 1) {
    return this.makeRequest('/movie/now_playing', { page });
  }

  // Get upcoming movies
  async getUpcomingMovies(page = 1) {
    return this.makeRequest('/movie/upcoming', { page });
  }

  // Get TV show season details
  async getTVSeasonDetails(tvId, seasonNumber) {
    return this.makeRequest(`/tv/${tvId}/season/${seasonNumber}`);
  }

  // Helper method to get full image URL
  getImageUrl(path, size = 'w500') {
    if (!path) return null;
    return `${this.imageBaseUrl}/${size}${path}`;
  }

  // Helper method to get backdrop URL
  getBackdropUrl(path, size = 'w1280') {
    if (!path) return null;
    return `${this.imageBaseUrl}/${size}${path}`;
  }

  // Helper method to get poster URL
  getPosterUrl(path, size = 'w500') {
    if (!path) return null;
    return `${this.imageBaseUrl}/${size}${path}`;
  }
}

export default new TMDBApi();
