import React, { memo } from 'react';
import {
  View,
  Text,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { slothStyles, SLOTH_COLORS } from '../styles/sloth';
import tmdbApi from '../services/tmdbApi';

const CastCard = memo(({ castMember }) => {
  const imageUrl = castMember.profile_path
    ? tmdbApi.getImageUrl(castMember.profile_path, 'w185')
    : null;

  return (
    <View style={slothStyles.castCardContainer}>
      {imageUrl ? (
        <Image
          source={{ uri: imageUrl }}
          style={slothStyles.castCardImage}
        />
      ) : (
        <View style={[slothStyles.castCardImage, { justifyContent: 'center', alignItems: 'center' }]}>
          <Ionicons name="person" size={40} color={SLOTH_COLORS.textSecondary} />
        </View>
      )}
      <Text style={slothStyles.castCardName} numberOfLines={2}>
        {castMember.name}
      </Text>
      <Text style={slothStyles.castCardCharacter} numberOfLines={2}>
        {castMember.character}
      </Text>
    </View>
  );
});

export default CastCard;
