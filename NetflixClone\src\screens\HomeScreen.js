import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  SafeAreaView,
  StatusBar,
  RefreshControl,
  ActivityIndicator,
  Text,
  TouchableOpacity,
  ImageBackground,
  Image,
  useWindowDimensions, // NEW: Import hook for screen dimensions
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import tmdbApi from '../services/tmdbApi';
import MediaRow from '../components/MediaRow';
import { slothStyles, SLOTH_COLORS } from '../styles/sloth';

const HomeScreen = ({ navigation }) => {
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [heroItem, setHeroItem] = useState(null);
  const [popularMovies, setPopularMovies] = useState([]);
  const [topRatedTv, setTopRatedTv] = useState([]);
  const [trending, setTrending] = useState([]);
  const [refreshKey, setRefreshKey] = useState(0);

  // This hook provides width and height and updates on screen rotation
  const { width, height } = useWindowDimensions();
  // Determine if the device is in landscape mode
  const isLandscape = width > height;

  useEffect(() => {
    // We pass true on the first load to set the loading spinner
    loadHomeData(true);
  }, []);

  const loadHomeData = async (isInitialLoad = false) => {
    if (isInitialLoad) {
      setLoading(true);
    }
    try {
      const [
        trendingData,
        popularMoviesData,
        topRatedTvData,
      ] = await Promise.all([
        tmdbApi.getTrending('all'),
        tmdbApi.getPopularMovies(),
        tmdbApi.getTopRatedTV(),
      ]);

      setTrending(trendingData.results || []);
      setPopularMovies(popularMoviesData.results || []);
      setTopRatedTv(topRatedTvData.results || []);

      // Safer and more robust randomization logic
      if (trendingData.results && trendingData.results.length > 0) {
        const itemsWithBackdrops = trendingData.results.filter(item => item.backdrop_path);
        
        if (itemsWithBackdrops.length > 0) {
          const randomIndex = Math.floor(Math.random() * itemsWithBackdrops.length);
          setHeroItem(itemsWithBackdrops[randomIndex]);
        } else {
          // Fallback if no items have a backdrop, find the first valid one
          setHeroItem(trendingData.results.find(item => item.poster_path) || null);
        }
      }
    } catch (error) {
      console.error('Error loading home data:', error);
    } finally {
      if (isInitialLoad) {
        setLoading(false);
      }
      setRefreshing(false); // Always turn off refreshing state
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    setRefreshKey(prevKey => prevKey + 1); // This forces MediaRow to re-render
    await loadHomeData(false); // Pass false so it doesn't show the initial loading spinner
  };

  const handleItemPress = (item) => {
    const mediaType = item.media_type || (item.first_air_date ? 'tv' : 'movie');
    navigation.navigate('MediaDetail', { item: { ...item, media_type: mediaType }, mediaType });
  };

  const handlePlayPress = (item) => {
    const mediaType = item.media_type || (item.first_air_date ? 'tv' : 'movie');
    navigation.navigate('Player', { item: { ...item, media_type: mediaType } });
  };
  
  if (loading) {
    return (
      <View style={slothStyles.loadingContainer}>
        <ActivityIndicator size="large" color={SLOTH_COLORS.primary} />
      </View>
    );
  }

  const renderHeroMetadata = () => {
    if (!heroItem) return null;
    const isTvShow = heroItem.media_type === 'tv' || heroItem.first_air_date;
    
    return (
        <View style={slothStyles.heroMetadata}>
           <Text style={slothStyles.metadataText}>{new Date(heroItem.release_date || heroItem.first_air_date).getFullYear()}</Text>
           {isTvShow && heroItem.number_of_seasons && (
              <Text style={slothStyles.metadataText}>{heroItem.number_of_seasons} Season{heroItem.number_of_seasons > 1 ? 's' : ''}</Text>
           )}
           <Text style={slothStyles.badge}>
              {heroItem.adult ? 'TV-MA' : 'PG-13'}
           </Text>
        </View>
    );
  };
  
  return (
    <View style={slothStyles.container}>
      <StatusBar
  translucent
  backgroundColor="transparent"
  barStyle="light-content" // or "dark-content" based on your UI
/>
        <ScrollView
            showsVerticalScrollIndicator={false}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                tintColor={SLOTH_COLORS.white}
              />
            }
        >
        {heroItem && (
          // The component's style now includes a dynamic height
          <ImageBackground
            source={{ uri: tmdbApi.getBackdropUrl(heroItem.backdrop_path) }}
            style={[
              slothStyles.heroBanner, 
              // Set height for portrait or a widescreen ratio for landscape
              { height: isLandscape ? width * 0.5625 : height * 0.6 }
            ]}
          >
            <LinearGradient
              colors={['rgba(15, 16, 20, 0.2)', 'rgba(15, 16, 20, 0.7)', SLOTH_COLORS.background]}
              style={slothStyles.heroGradient}
            >
              <SafeAreaView>
                 <View style={slothStyles.header}>
                  <Text style={slothStyles.logo}>S</Text>
                  <View style={slothStyles.headerIcons}>
                    <TouchableOpacity 
                      style={slothStyles.iconContainer}
                      onPress={() => navigation.navigate('Search')}
                    >
                      <Ionicons name="search-outline" style={slothStyles.icon} />
                    </TouchableOpacity>
                  </View>
                 </View>
              </SafeAreaView>

              <View style={slothStyles.heroContent}>
                <View style={slothStyles.heroOriginalsContainer}></View>
                <Text style={slothStyles.heroTitle} numberOfLines={2} adjustsFontSizeToFit>{heroItem.title || heroItem.name}</Text>
                {renderHeroMetadata()}
                <View style={slothStyles.heroButtons}>
                  <TouchableOpacity 
                    style={slothStyles.playButton} 
                    onPress={() => handlePlayPress(heroItem)}
                  >
                    <Ionicons name="play" size={22} color={SLOTH_COLORS.dark} />
                    <Text style={slothStyles.playButtonText}>Play</Text>
                  </TouchableOpacity>
                  <TouchableOpacity 
                    style={slothStyles.infoButton} 
                    onPress={() => handleItemPress(heroItem)}
                  >
                    <Ionicons name="information-circle-outline" size={22} color={SLOTH_COLORS.white} />
                    <Text style={slothStyles.infoButtonText}>More Info</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </LinearGradient>
          </ImageBackground>
        )}
        <MediaRow
            key={`popular-${refreshKey}`}
            title="Popular Media"
            data={popularMovies}
            onItemPress={handleItemPress}
        />
        <MediaRow
            key={`trending-${refreshKey}`}
            title="Trending Now"
            data={trending}
            onItemPress={handleItemPress}
        />
        <MediaRow
            key={`top-rated-${refreshKey}`}
            title="Top Rated TV"
            data={topRatedTv}
            onItemPress={handleItemPress}
        />
        </ScrollView>
    </View>
  );
};

export default HomeScreen;