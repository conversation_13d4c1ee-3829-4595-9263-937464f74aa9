import React, { useState, useEffect } from 'react';
import { 
  View, 
  TextInput, 
  TouchableOpacity, 
  StyleSheet, 
  LayoutAnimation, 
  UIManager, 
  Platform 
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { SLOTH_COLORS } from '../styles/sloth';

// Enable LayoutAnimation on Android
if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

const SearchBar = ({ onSearch, onClear, onBackPress, autoFocus = false }) => {
  const [query, setQuery] = useState('');

  // When the query text changes, we trigger a layout animation
  // so the 'Cancel' text button appears smoothly.
  const handleTextChange = (text) => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setQuery(text);
    // Live search - triggers search on every keystroke
    onSearch(text.trim()); 
  };

  const handleClearPress = () => {
    handleTextChange(''); // Animate the clear button away
    onClear();
  };

  return (
    <View style={styles.container}>
      <TouchableOpacity onPress={onBackPress} style={styles.backButton}>
        <Ionicons name="arrow-back" size={24} color={SLOTH_COLORS.white} />
      </TouchableOpacity>
      
      <View style={styles.inputContainer}>
        <Ionicons name="search" size={20} color={SLOTH_COLORS.textSecondary} style={styles.icon} />
        <TextInput
          style={styles.input}
          placeholder="Search..."
          placeholderTextColor={SLOTH_COLORS.textSecondary}
          value={query}
          onChangeText={handleTextChange}
          returnKeyType="search"
          autoFocus={autoFocus}
          selectionColor={SLOTH_COLORS.primary}
        />
        {query.length > 0 && (
          <TouchableOpacity onPress={handleClearPress}>
            <Ionicons name="close-circle" size={22} color={SLOTH_COLORS.textSecondary} style={styles.clearIcon} />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 10,
    paddingBottom: 10,
    width: '100%',
  },
  backButton: {
    padding: 5,
    marginRight: 5,
  },
  inputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: SLOTH_COLORS.infoButtonBg,
    borderRadius: 12,
    height: 48,
    paddingHorizontal: 12,
  },
  icon: {
    marginRight: 8,
  },
  input: {
    flex: 1,
    color: SLOTH_COLORS.white,
    fontSize: 17,
    height: '100%',
  },
  clearIcon: {
    marginLeft: 8,
  }
});

export default SearchBar;